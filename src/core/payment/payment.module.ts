import { Global, Module } from '@nestjs/common';
import {
  NotificationRepository,
  PaymentMethodRepository,
  PaymentRepository,
  UserRepository,
} from 'src/common/repository';
import { TransactionEntity } from 'src/transactions/transaction/entity/tranaction';
import { NotificationsService } from '../notifications/notifications.service';
import { PaymentService } from './payment.service';

@Global()
@Module({
  providers: [
    PaymentRepository,
    PaymentMethodRepository,
    TransactionEntity,
    NotificationsService,
    NotificationRepository,
    UserRepository,
    PaymentService,
  ],
  exports: [PaymentService],
})
export class PaymentModule {}
